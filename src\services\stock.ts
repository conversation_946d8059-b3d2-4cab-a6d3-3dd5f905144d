// import type { PageParams } from 'src/types/pagination';
import { api } from 'src/boot/axios'
import type { Stock } from 'src/types/stock'
// import { HttpStatusCode } from 'axios';

export class StockService {
  static path = 'stock'

  static async getAll(page = 1, limit = 12) {
    const res = await api.get(`${this.path}?page=${page}&limit=${limit}`)
    return res.data
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`)
    return res.data
  }

  static async getOneByProductId(id: number) {
    const res = await api.get(`${this.path}/product/${id}`)
    return res.data
  }

  static async updateOne(id: number, obj: Partial<Stock>) {
    const res = await api.put(`${this.path}/${id}/`, obj)
    return res.data
  }
  static async getAllByFilter(search = '', filter = '', branch = '', page = 1, limit = 12) {
    console.log(branch)
    const res = await api.post(this.path, {
      search,
      filter,
      branch,
      page,
      limit,
    })
    return res.data
  }
  static async getAllByFilterDialog(
    search = '',
    filter = '',
    branch = '',
    distributor = '',
    page = 1,
    limit = 12,
  ) {
    console.log(branch)
    const res = await api.post(this.path, {
      search,
      filter,
      branch,
      distributor,
      page,
      limit,
    })
    return res.data
  }
  static async getSummaryByProduct(search = '', product_id = '') {
    const res = await api.post(`${this.path}/summary`, {
      search,
      product_id,
    })
    console.log(res.data)
    return res.data
  }
}
